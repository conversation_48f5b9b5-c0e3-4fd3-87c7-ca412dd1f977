package de.sarocesch.sarossecuritycheckermod;

import com.mojang.logging.LogUtils;
import de.sarocesch.sarossecuritycheckermod.config.SecurityConfig;
import de.sarocesch.sarossecuritycheckermod.util.FileHashScanner;
import de.sarocesch.sarossecuritycheckermod.util.ServerHashListFetcher;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.screens.DisconnectedScreen;
import net.minecraft.client.gui.screens.TitleScreen;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.config.ModConfig;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.slf4j.Logger;

import java.util.List;

// The value here should match an entry in the META-INF/mods.toml file
@Mod(SarosSecurityCheckerMod.MODID)
public class SarosSecurityCheckerMod
{
    // Define mod id in a common place for everything to reference
    public static final String MODID = "sarossecuritycheckermod";
    // Directly reference a slf4j logger
    private static final Logger LOGGER = LogUtils.getLogger();

    public SarosSecurityCheckerMod(FMLJavaModLoadingContext context)
    {
        IEventBus modEventBus = context.getModEventBus();

        // Register the commonSetup method for modloading
        modEventBus.addListener(this::commonSetup);

        // Register ourselves for server and other game events we are interested in
        MinecraftForge.EVENT_BUS.register(this);

        // Register our mod's configuration
        context.registerConfig(ModConfig.Type.CLIENT, SecurityConfig.SPEC);
    }

    private void commonSetup(final FMLCommonSetupEvent event)
    {
        // Some common setup code
        LOGGER.info("Saro's Security Checker Mod - Common Setup");
    }

    // Event handler for player login - this will handle both singleplayer and multiplayer
    @SubscribeEvent
    public void onPlayerLoggedIn(PlayerEvent.PlayerLoggedInEvent event)
    {
        LOGGER.info("Player logged in: {}", event.getEntity().getName().getString());

        // Check if this is a singleplayer world
        if (Minecraft.getInstance().hasSingleplayerServer())
        {
            if (SecurityConfig.isSingleplayerScanningEnabled()) {
                LOGGER.info("Singleplayer world detected - scanning for file hashes");
                displayFileHashesInChat();
            } else {
                LOGGER.info("Singleplayer scanning is disabled in configuration");
            }
        }
        else
        {
            if (SecurityConfig.isMultiplayerValidationEnabled()) {
                LOGGER.info("Multiplayer server detected - will validate against server hash lists");
                validateAgainstServerHashLists();
            } else {
                LOGGER.info("Multiplayer validation is disabled in configuration");
            }
        }
    }

    /**
     * Scans files and displays their hashes in the chat
     */
    private void displayFileHashesInChat()
    {
        // Run the scanning in a separate thread to avoid blocking the main thread
        new Thread(() -> {
            try {
                LOGGER.info("Starting file hash scan...");
                List<FileHashScanner.FileHashInfo> hashes = FileHashScanner.scanAllFiles();

                // Format the hashes for chat display
                List<String> chatMessages = FileHashScanner.formatForChat(hashes);

                // Send messages to chat on the main thread
                Minecraft.getInstance().execute(() -> {
                    if (Minecraft.getInstance().player != null && SecurityConfig.shouldShowChatNotifications()) {
                        // Send header message
                        Minecraft.getInstance().player.sendSystemMessage(
                            Component.literal("§6=== SARO'S SECURITY CHECKER - FILE HASHES ===")
                        );

                        // Limit the number of files displayed if configured
                        List<String> messagesToShow = chatMessages;
                        int maxFiles = SecurityConfig.getMaxFilesToDisplay();
                        if (maxFiles > 0 && chatMessages.size() > maxFiles) {
                            messagesToShow = chatMessages.subList(0, maxFiles);
                            messagesToShow.add("§7... and " + (chatMessages.size() - maxFiles) + " more files (limit reached)");
                        }

                        // Send each hash message
                        for (String message : messagesToShow) {
                            if (!message.isEmpty()) {
                                Minecraft.getInstance().player.sendSystemMessage(
                                    Component.literal(message)
                                );
                            }
                        }

                        // Send footer message
                        Minecraft.getInstance().player.sendSystemMessage(
                            Component.literal("§6=== SCAN COMPLETE - " + hashes.size() + " FILES SCANNED ===")
                        );
                    }

                    LOGGER.info("File hash scan completed. {} files scanned.", hashes.size());
                });

            } catch (Exception e) {
                LOGGER.error("Error during file hash scanning", e);

                // Send error message to chat on the main thread
                Minecraft.getInstance().execute(() -> {
                    if (Minecraft.getInstance().player != null) {
                        Minecraft.getInstance().player.sendSystemMessage(
                            Component.literal("§cError: Failed to scan files for hashes. Check logs for details.")
                        );
                    }
                });
            }
        }, "FileHashScanner").start();
    }

    /**
     * Validates local files against server hash lists and disconnects if violations are found
     */
    private void validateAgainstServerHashLists()
    {
        // Run the validation in a separate thread to avoid blocking the main thread
        new Thread(() -> {
            try {
                LOGGER.info("Starting server hash validation...");

                // First, scan local files
                List<FileHashScanner.FileHashInfo> localFiles = FileHashScanner.scanAllFiles();
                LOGGER.info("Scanned {} local files for validation", localFiles.size());

                // Try to fetch hash lists from the server
                // For now, we'll use a hardcoded URL - this should be configurable in the future
                String serverHashListUrl = getServerHashListUrl();

                if (serverHashListUrl == null) {
                    LOGGER.info("No server hash list URL configured - skipping validation");
                    return;
                }

                ServerHashListFetcher.HashLists serverHashLists = ServerHashListFetcher.fetchHashLists(serverHashListUrl);

                if (!serverHashLists.hasWhitelist && !serverHashLists.hasBlacklist) {
                    LOGGER.info("Server has no hash restrictions - validation passed");
                    return;
                }

                // Validate files against server hash lists
                List<ServerHashListFetcher.ValidationFailure> failures =
                    ServerHashListFetcher.validateFiles(localFiles, serverHashLists);

                if (failures.isEmpty()) {
                    LOGGER.info("All files passed server validation");

                    // Send success message to chat on the main thread
                    Minecraft.getInstance().execute(() -> {
                        if (Minecraft.getInstance().player != null &&
                            SecurityConfig.shouldShowChatNotifications() &&
                            SecurityConfig.shouldShowSuccessMessages()) {
                            Minecraft.getInstance().player.sendSystemMessage(
                                Component.literal("§aServer security validation passed - all files are approved")
                            );
                        }
                    });
                } else {
                    LOGGER.warn("Server validation failed - {} files violated security policy", failures.size());

                    // Handle security violations on the main thread
                    Minecraft.getInstance().execute(() -> {
                        if (SecurityConfig.shouldAutoDisconnect()) {
                            disconnectWithSecurityViolation(failures);
                        } else {
                            // Just show warning message without disconnecting
                            showSecurityViolationWarning(failures);
                        }
                    });
                }

            } catch (Exception e) {
                LOGGER.error("Error during server hash validation", e);

                // Send error message and potentially disconnect on the main thread
                Minecraft.getInstance().execute(() -> {
                    if (Minecraft.getInstance().player != null) {
                        Minecraft.getInstance().player.sendSystemMessage(
                            Component.literal("§cError: Failed to validate files against server. Check logs for details.")
                        );
                    }
                });
            }
        }, "ServerHashValidator").start();
    }

    /**
     * Gets the server hash list URL from configuration
     */
    private String getServerHashListUrl()
    {
        return SecurityConfig.getHashListUrl();
    }

    /**
     * Disconnects from the server with a security violation message
     */
    private void disconnectWithSecurityViolation(List<ServerHashListFetcher.ValidationFailure> failures)
    {
        // Build the disconnect message
        StringBuilder message = new StringBuilder("Security Violation Detected!\n\n");
        message.append("The following files are not allowed by this server:\n");

        for (ServerHashListFetcher.ValidationFailure failure : failures) {
            message.append("- ").append(failure.getErrorMessage()).append("\n");
        }

        message.append("\nPlease remove the prohibited files and reconnect.");

        // Send the message to chat first if player is available
        if (Minecraft.getInstance().player != null) {
            Minecraft.getInstance().player.sendSystemMessage(
                Component.literal("§c" + message.toString())
            );
        }

        // Disconnect from the server by showing the disconnected screen
        LOGGER.info("Disconnecting from server due to security violations");

        // Create the disconnect screen with the security violation message
        Component disconnectReason = Component.literal("Security Validation Failed");
        Component disconnectMessage = Component.literal(message.toString());

        // Show the disconnected screen which will handle the disconnection
        Minecraft.getInstance().setScreen(new DisconnectedScreen(
            new TitleScreen(), // Parent screen to return to
            disconnectReason,  // Title
            disconnectMessage  // Message
        ));

        // Also disconnect the connection if it exists
        if (Minecraft.getInstance().getConnection() != null) {
            Minecraft.getInstance().level.disconnect();
        }
    }

    /**
     * Shows a security violation warning without disconnecting
     */
    private void showSecurityViolationWarning(List<ServerHashListFetcher.ValidationFailure> failures)
    {
        if (!SecurityConfig.shouldShowChatNotifications()) {
            return;
        }

        // Build the warning message
        StringBuilder message = new StringBuilder("§cSecurity Warning!\n\n");
        message.append("§eThe following files are not allowed by this server:\n");

        for (ServerHashListFetcher.ValidationFailure failure : failures) {
            message.append("§7- ").append(failure.getErrorMessage()).append("\n");
        }

        message.append("\n§fConsider removing these files. Auto-disconnect is disabled in configuration.");

        // Send the warning to chat
        if (Minecraft.getInstance().player != null) {
            Minecraft.getInstance().player.sendSystemMessage(Component.literal(message.toString()));
        }

        LOGGER.warn("Security violations detected but auto-disconnect is disabled");
    }

    // You can use EventBusSubscriber to automatically register all static methods in the class annotated with @SubscribeEvent
    @Mod.EventBusSubscriber(modid = MODID, bus = Mod.EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
    public static class ClientModEvents
    {
        @SubscribeEvent
        public static void onClientSetup(FMLClientSetupEvent event)
        {
            // Some client setup code
            LOGGER.info("Saro's Security Checker Mod - Client Setup");
            LOGGER.info("Player: {}", Minecraft.getInstance().getUser().getName());
        }
    }
}
