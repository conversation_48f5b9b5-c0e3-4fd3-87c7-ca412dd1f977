package de.sarocesch.sarossecuritycheckermod;

import com.mojang.logging.LogUtils;
import de.sarocesch.sarossecuritycheckermod.config.SecurityConfig;
import de.sarocesch.sarossecuritycheckermod.util.FileHashScanner;
import de.sarocesch.sarossecuritycheckermod.util.ServerHashDetector;
import de.sarocesch.sarossecuritycheckermod.util.ServerHashListFetcher;
import de.sarocesch.sarossecuritycheckermod.util.TranslationHelper;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.screens.DisconnectedScreen;
import net.minecraft.client.gui.screens.TitleScreen;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.config.ModConfig;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.slf4j.Logger;

import java.util.List;

// The value here should match an entry in the META-INF/mods.toml file
@Mod(SarosSecurityCheckerMod.MODID)
public class SarosSecurityCheckerMod
{
    // Define mod id in a common place for everything to reference
    public static final String MODID = "sarossecuritycheckermod";
    // Directly reference a slf4j logger
    private static final Logger LOGGER = LogUtils.getLogger();

    public SarosSecurityCheckerMod(FMLJavaModLoadingContext context)
    {
        IEventBus modEventBus = context.getModEventBus();

        // Register the commonSetup method for modloading
        modEventBus.addListener(this::commonSetup);

        // Register ourselves for server and other game events we are interested in
        MinecraftForge.EVENT_BUS.register(this);

        // Register our mod's configuration
        context.registerConfig(ModConfig.Type.CLIENT, SecurityConfig.SPEC);
    }

    private void commonSetup(final FMLCommonSetupEvent event)
    {
        // Some common setup code
        LOGGER.info("Saro's Security Checker Mod - Common Setup");
    }

    // Event handler for player login - this will handle both singleplayer and multiplayer
    @SubscribeEvent
    public void onPlayerLoggedIn(PlayerEvent.PlayerLoggedInEvent event)
    {
        LOGGER.info("Player logged in: {}", event.getEntity().getName().getString());

        // Check if this is a singleplayer world
        if (Minecraft.getInstance().hasSingleplayerServer())
        {
            if (SecurityConfig.isSingleplayerScanningEnabled()) {
                if (SecurityConfig.isDebugModeEnabled()) {
                    LOGGER.info("Singleplayer world detected - scanning for file hashes");
                    displayFileHashesInChat();
                } else {
                    LOGGER.debug("Singleplayer world detected - scanning for file hashes (debug mode disabled, no chat output)");
                    // Still scan files for potential future use, but don't display in chat
                    scanFilesQuietly();
                }
            } else {
                LOGGER.info(TranslationHelper.getConfigSingleplayerDisabled());
            }
        }
        else
        {
            if (SecurityConfig.isMultiplayerValidationEnabled()) {
                LOGGER.info("Multiplayer server detected - will validate against server hash lists");
                validateAgainstServerHashLists();
            } else {
                LOGGER.info(TranslationHelper.getConfigMultiplayerDisabled());
            }
        }
    }

    /**
     * Scans files and displays their hashes in the chat
     */
    private void displayFileHashesInChat()
    {
        // Run the scanning in a separate thread to avoid blocking the main thread
        new Thread(() -> {
            try {
                if (SecurityConfig.isDebugModeEnabled()) {
                    LOGGER.info(TranslationHelper.getLogScanStarted());
                }
                List<FileHashScanner.FileHashInfo> hashes = FileHashScanner.scanAllFiles();

                // Format the hashes for chat display
                List<String> chatMessages = FileHashScanner.formatForChat(hashes);

                // Send messages to chat on the main thread
                Minecraft.getInstance().execute(() -> {
                    if (Minecraft.getInstance().player != null && SecurityConfig.shouldShowChatNotifications() && SecurityConfig.isDebugModeEnabled()) {
                        // Send header message
                        Minecraft.getInstance().player.sendSystemMessage(
                            TranslationHelper.getSingleplayerHeader()
                        );

                        // Limit the number of files displayed if configured
                        List<String> messagesToShow = chatMessages;
                        int maxFiles = SecurityConfig.getMaxFilesToDisplay();
                        if (maxFiles > 0 && chatMessages.size() > maxFiles) {
                            messagesToShow = chatMessages.subList(0, maxFiles);
                            messagesToShow.add(TranslationHelper.getSingleplayerLimitReached(chatMessages.size() - maxFiles).getString());
                        }

                        // Send each hash message
                        for (String message : messagesToShow) {
                            if (!message.isEmpty()) {
                                Minecraft.getInstance().player.sendSystemMessage(
                                    Component.literal(message)
                                );
                            }
                        }

                        // Send footer message
                        Minecraft.getInstance().player.sendSystemMessage(
                            TranslationHelper.getSingleplayerFooter(hashes.size())
                        );
                    }

                    if (SecurityConfig.isDebugModeEnabled()) {
                        LOGGER.info(TranslationHelper.getLogScanCompleted(hashes.size()));
                    } else {
                        LOGGER.debug(TranslationHelper.getLogScanCompleted(hashes.size()));
                    }
                });

            } catch (Exception e) {
                LOGGER.error("Error during file hash scanning", e);

                // Send error message to chat on the main thread
                Minecraft.getInstance().execute(() -> {
                    if (Minecraft.getInstance().player != null) {
                        Minecraft.getInstance().player.sendSystemMessage(
                            TranslationHelper.getErrorScanFailed()
                        );
                    }
                });
            }
        }, "FileHashScanner").start();
    }

    /**
     * Scans files quietly without displaying results in chat (for non-debug mode)
     */
    private void scanFilesQuietly()
    {
        // Run the scanning in a separate thread to avoid blocking the main thread
        new Thread(() -> {
            try {
                if (SecurityConfig.isDebugModeEnabled()) {
                    LOGGER.debug(TranslationHelper.getLogScanStarted());
                }
                List<FileHashScanner.FileHashInfo> hashes = FileHashScanner.scanAllFiles();

                if (SecurityConfig.isDebugModeEnabled()) {
                    LOGGER.debug(TranslationHelper.getLogScanCompleted(hashes.size()));
                } else {
                    LOGGER.info("File scan completed silently. {} files processed.", hashes.size());
                }

            } catch (Exception e) {
                LOGGER.error("Error during quiet file hash scanning", e);
            }
        }, "QuietFileHashScanner").start();
    }

    /**
     * Validates local files against server hash lists and disconnects if violations are found
     */
    private void validateAgainstServerHashLists()
    {
        // Run the validation in a separate thread to avoid blocking the main thread
        new Thread(() -> {
            try {
                if (SecurityConfig.isDebugModeEnabled()) {
                    LOGGER.info(TranslationHelper.getLogValidationStarted());
                } else {
                    LOGGER.debug(TranslationHelper.getLogValidationStarted());
                }

                // First, scan local files
                List<FileHashScanner.FileHashInfo> localFiles = FileHashScanner.scanAllFiles();
                if (SecurityConfig.isDebugModeEnabled()) {
                    LOGGER.info("Scanned {} local files for validation", localFiles.size());
                } else {
                    LOGGER.debug("Scanned {} local files for validation", localFiles.size());
                }

                // Try to fetch hash lists from the server
                ServerHashListFetcher.HashLists serverHashLists = null;

                // First try automatic detection
                if (SecurityConfig.shouldAutoDetectServerHashFile()) {
                    if (SecurityConfig.isDebugModeEnabled()) {
                        LOGGER.info(TranslationHelper.getMultiplayerAutoDetection());
                    } else {
                        LOGGER.debug(TranslationHelper.getMultiplayerAutoDetection());
                    }
                    serverHashLists = ServerHashDetector.autoDetectServerHashFile();
                }

                // If auto-detection failed, try configured URL
                if (serverHashLists == null) {
                    String serverHashListUrl = getServerHashListUrl();
                    if (serverHashListUrl != null) {
                        if (SecurityConfig.isDebugModeEnabled()) {
                            LOGGER.info(TranslationHelper.getMultiplayerAutoDetectionFailed(serverHashListUrl));
                        } else {
                            LOGGER.debug(TranslationHelper.getMultiplayerAutoDetectionFailed(serverHashListUrl));
                        }
                        serverHashLists = ServerHashListFetcher.fetchHashLists(serverHashListUrl);
                    }
                }

                // If we still don't have hash lists, skip validation
                if (serverHashLists == null) {
                    if (SecurityConfig.isDebugModeEnabled()) {
                        LOGGER.info(TranslationHelper.getMultiplayerNoHashLists());
                    } else {
                        LOGGER.debug(TranslationHelper.getMultiplayerNoHashLists());
                    }
                    return;
                }

                if (!serverHashLists.hasWhitelist && !serverHashLists.hasBlacklist) {
                    LOGGER.info("Server has no hash restrictions - validation passed");
                    return;
                }

                // Validate files against server hash lists
                List<ServerHashListFetcher.ValidationFailure> failures =
                    ServerHashListFetcher.validateFiles(localFiles, serverHashLists);

                // Get detailed validation statistics
                ServerHashListFetcher.ValidationStats stats =
                    ServerHashListFetcher.getValidationStats(localFiles, serverHashLists);

                if (SecurityConfig.isDebugModeEnabled()) {
                    LOGGER.info(TranslationHelper.getMultiplayerValidationComplete(stats.getSummary()));
                } else {
                    LOGGER.debug(TranslationHelper.getMultiplayerValidationComplete(stats.getSummary()));
                }

                if (failures.isEmpty()) {
                    if (SecurityConfig.isDebugModeEnabled()) {
                        LOGGER.info(TranslationHelper.getLogValidationPassed());
                    } else {
                        LOGGER.debug(TranslationHelper.getLogValidationPassed());
                    }

                    // Send success message to chat on the main thread
                    Minecraft.getInstance().execute(() -> {
                        if (Minecraft.getInstance().player != null &&
                            SecurityConfig.shouldShowChatNotifications() &&
                            SecurityConfig.shouldShowSuccessMessages()) {
                            Minecraft.getInstance().player.sendSystemMessage(
                                TranslationHelper.getMultiplayerValidationPassed(stats.getSummary())
                            );
                        }
                    });
                } else {
                    LOGGER.warn(TranslationHelper.getLogValidationFailed(failures.size()));

                    // Log detailed failure information
                    for (ServerHashListFetcher.ValidationFailure failure : failures) {
                        LOGGER.warn("Validation failure: {}", failure.getDetailedErrorMessage());
                    }

                    // Handle security violations on the main thread
                    Minecraft.getInstance().execute(() -> {
                        if (SecurityConfig.shouldAutoDisconnect()) {
                            disconnectWithSecurityViolation(failures, stats);
                        } else {
                            // Just show warning message without disconnecting
                            showSecurityViolationWarning(failures, stats);
                        }
                    });
                }

            } catch (Exception e) {
                LOGGER.error("Error during server hash validation", e);

                // Send error message and potentially disconnect on the main thread
                Minecraft.getInstance().execute(() -> {
                    if (Minecraft.getInstance().player != null) {
                        Minecraft.getInstance().player.sendSystemMessage(
                            TranslationHelper.getErrorValidationFailed()
                        );
                    }
                });
            }
        }, "ServerHashValidator").start();
    }

    /**
     * Gets the server hash list URL from configuration
     */
    private String getServerHashListUrl()
    {
        return SecurityConfig.getHashListUrl();
    }

    /**
     * Disconnects from the server with a security violation message
     */
    private void disconnectWithSecurityViolation(List<ServerHashListFetcher.ValidationFailure> failures, ServerHashListFetcher.ValidationStats stats)
    {
        // Build the disconnect message with detailed statistics
        StringBuilder message = new StringBuilder(TranslationHelper.getSecurityViolationDetected() + "\n\n");
        message.append(TranslationHelper.getSecurityValidationSummary(stats.getSummary())).append("\n\n");
        message.append(TranslationHelper.getSecurityFilesNotAllowed()).append("\n");

        for (ServerHashListFetcher.ValidationFailure failure : failures) {
            message.append("- ").append(failure.getErrorMessage()).append("\n");
        }

        message.append("\n").append(TranslationHelper.getSecurityRemoveFiles());

        // Send the message to chat first if player is available
        if (Minecraft.getInstance().player != null) {
            Minecraft.getInstance().player.sendSystemMessage(
                Component.literal("§c" + message.toString())
            );
        }

        // Disconnect from the server by showing the disconnected screen
        LOGGER.info("Disconnecting from server due to security violations");

        // Create the disconnect screen with the security violation message
        Component disconnectReason = TranslationHelper.getSecurityDisconnectReason();
        Component disconnectMessage = Component.literal(message.toString());

        // Show the disconnected screen which will handle the disconnection
        Minecraft.getInstance().setScreen(new DisconnectedScreen(
            new TitleScreen(), // Parent screen to return to
            disconnectReason,  // Title
            disconnectMessage  // Message
        ));

        // Also disconnect the connection if it exists
        if (Minecraft.getInstance().getConnection() != null) {
            Minecraft.getInstance().level.disconnect();
        }
    }

    /**
     * Shows a security violation warning without disconnecting
     */
    private void showSecurityViolationWarning(List<ServerHashListFetcher.ValidationFailure> failures, ServerHashListFetcher.ValidationStats stats)
    {
        if (!SecurityConfig.shouldShowChatNotifications()) {
            return;
        }

        // Build the warning message with detailed statistics
        StringBuilder message = new StringBuilder();
        message.append(TranslationHelper.getSecurityWarning().getString()).append("\n\n");
        message.append("§e").append(TranslationHelper.getSecurityValidationSummary(stats.getSummary())).append("\n\n");
        message.append("§e").append(TranslationHelper.getSecurityFilesNotAllowed()).append("\n");

        for (ServerHashListFetcher.ValidationFailure failure : failures) {
            message.append("§7- ").append(failure.getErrorMessage()).append("\n");
        }

        message.append("\n").append(TranslationHelper.getSecurityAutoDisconnectDisabled());

        // Send the warning to chat
        if (Minecraft.getInstance().player != null) {
            Minecraft.getInstance().player.sendSystemMessage(Component.literal(message.toString()));
        }

        LOGGER.warn(TranslationHelper.getLogViolationsDetected());
    }

    // You can use EventBusSubscriber to automatically register all static methods in the class annotated with @SubscribeEvent
    @Mod.EventBusSubscriber(modid = MODID, bus = Mod.EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
    public static class ClientModEvents
    {
        @SubscribeEvent
        public static void onClientSetup(FMLClientSetupEvent event)
        {
            // Some client setup code
            LOGGER.info("Saro's Security Checker Mod - Client Setup");
            LOGGER.info("Player: {}", Minecraft.getInstance().getUser().getName());
        }
    }
}
