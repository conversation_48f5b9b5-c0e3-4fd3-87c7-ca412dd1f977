package de.sarocesch.sarossecuritycheckermod.util;

import com.mojang.logging.LogUtils;
import de.sarocesch.sarossecuritycheckermod.config.SecurityConfig;
import de.sarocesch.sarossecuritycheckermod.util.TranslationHelper;
import net.minecraft.client.Minecraft;
import net.minecraft.client.multiplayer.ServerData;
import org.slf4j.Logger;

import java.net.InetSocketAddress;
import java.util.ArrayList;
import java.util.List;

public class ServerHashDetector {
    private static final Logger LOGGER = LogUtils.getLogger();
    
    /**
     * Attempts to automatically detect and fetch hash validation files from the server
     * @return ServerHashListFetcher.HashLists if found, null if not found or disabled
     */
    public static ServerHashListFetcher.HashLists autoDetectServerHashFile() {
        if (!SecurityConfig.shouldAutoDetectServerHashFile()) {
            if (SecurityConfig.isDebugModeEnabled()) {
                LOGGER.debug(TranslationHelper.getLogAutoDetectionDisabled());
            }
            return null;
        }

        String serverAddress = getCurrentServerAddress();
        if (serverAddress == null) {
            if (SecurityConfig.isDebugModeEnabled()) {
                LOGGER.warn(TranslationHelper.getLogServerAddressUnknown());
            }
            return null;
        }
        
        if (SecurityConfig.isDebugModeEnabled()) {
            LOGGER.info("Attempting to auto-detect hash validation file for server: {}", serverAddress);
        }

        List<String> urlsToTry = generatePossibleUrls(serverAddress);

        for (String url : urlsToTry) {
            if (SecurityConfig.isDebugModeEnabled()) {
                LOGGER.debug("Trying URL: {}", url);
            }
            
            try {
                ServerHashListFetcher.HashLists hashLists = ServerHashListFetcher.fetchHashLists(url);
                
                // Check if we actually got some hash data
                if (hashLists != null && (hashLists.hasWhitelist || hashLists.hasBlacklist)) {
                    if (SecurityConfig.isDebugModeEnabled()) {
                        LOGGER.info(TranslationHelper.getLogHashFileFound(url));
                    }
                    return hashLists;
                }
            } catch (Exception e) {
                if (SecurityConfig.isDebugModeEnabled()) {
                    LOGGER.debug("Failed to fetch from {}: {}", url, e.getMessage());
                }
            }
        }
        
        if (SecurityConfig.isDebugModeEnabled()) {
            LOGGER.info(TranslationHelper.getLogHashFileNotFound(serverAddress));
        }
        return null;
    }
    
    /**
     * Gets the current server address from the Minecraft client
     */
    private static String getCurrentServerAddress() {
        Minecraft minecraft = Minecraft.getInstance();
        
        // Try to get from current server data
        if (minecraft.getCurrentServer() != null) {
            ServerData serverData = minecraft.getCurrentServer();
            return cleanServerAddress(serverData.ip);
        }
        
        // Try to get from connection
        if (minecraft.getConnection() != null && minecraft.getConnection().getConnection() != null) {
            try {
                InetSocketAddress address = (InetSocketAddress) minecraft.getConnection().getConnection().getRemoteAddress();
                if (address != null) {
                    return cleanServerAddress(address.getHostString());
                }
            } catch (Exception e) {
                LOGGER.debug("Could not extract address from connection: {}", e.getMessage());
            }
        }
        
        return null;
    }
    
    /**
     * Cleans and normalizes server address for URL generation
     */
    private static String cleanServerAddress(String address) {
        if (address == null) {
            return null;
        }
        
        // Remove port if present
        if (address.contains(":")) {
            address = address.substring(0, address.indexOf(":"));
        }
        
        // Remove any protocol prefix if somehow present
        if (address.startsWith("http://")) {
            address = address.substring(7);
        }
        if (address.startsWith("https://")) {
            address = address.substring(8);
        }
        
        return address.trim();
    }
    
    /**
     * Generates a list of possible URLs to check for hash validation files
     */
    private static List<String> generatePossibleUrls(String serverAddress) {
        List<String> urls = new ArrayList<>();
        String fileName = SecurityConfig.getServerHashFileName();
        
        // Try different URL patterns
        if (SecurityConfig.shouldTryHttpAndHttps()) {
            // HTTPS first (more secure)
            urls.add("https://" + serverAddress + "/" + fileName);
            urls.add("https://" + serverAddress + "/security/" + fileName);
            urls.add("https://" + serverAddress + "/minecraft/" + fileName);
            urls.add("https://" + serverAddress + "/mods/" + fileName);
            
            // Then HTTP as fallback
            urls.add("http://" + serverAddress + "/" + fileName);
            urls.add("http://" + serverAddress + "/security/" + fileName);
            urls.add("http://" + serverAddress + "/minecraft/" + fileName);
            urls.add("http://" + serverAddress + "/mods/" + fileName);
        } else {
            // Only try HTTPS if configured to not try both
            urls.add("https://" + serverAddress + "/" + fileName);
            urls.add("https://" + serverAddress + "/security/" + fileName);
            urls.add("https://" + serverAddress + "/minecraft/" + fileName);
            urls.add("https://" + serverAddress + "/mods/" + fileName);
        }
        
        return urls;
    }
    
    /**
     * Gets the hash list URL with automatic detection fallback
     */
    public static String getEffectiveHashListUrl() {
        // First try the configured URL
        String configUrl = SecurityConfig.getHashListUrl();
        if (configUrl != null && !configUrl.trim().isEmpty()) {
            return configUrl.trim();
        }
        
        // If auto-detection is enabled, try to build URL from current server
        if (SecurityConfig.shouldAutoDetectServerHashFile()) {
            String serverAddress = getCurrentServerAddress();
            if (serverAddress != null) {
                String fileName = SecurityConfig.getServerHashFileName();
                // Return the first URL we would try (HTTPS with root path)
                return "https://" + serverAddress + "/" + fileName;
            }
        }
        
        return null;
    }
}
