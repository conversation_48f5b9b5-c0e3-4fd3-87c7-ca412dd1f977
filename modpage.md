# 🛡️ <PERSON><PERSON>'s Security Checker Mod

**The ultimate client-side security solution for Minecraft servers and players**

Protect your Minecraft experience with comprehensive file hash validation that ensures server security without compromising performance. This powerful mod automatically scans and validates your mods and resource packs against server-defined security policies.

---

## ✨ **Key Features**

### 🔍 **Automatic File Scanning**
- **SHA-256 Hash Generation**: Creates secure hashes for all files in your `mods` and `resourcepacks` folders
- **Real-time Scanning**: Automatically scans files when joining singleplayer worlds
- **Comprehensive Coverage**: Recursively scans all subdirectories for complete protection

### 🌐 **Smart Server Validation**
- **Automatic Detection**: Intelligently detects server hash validation files without manual configuration
- **Multi-Protocol Support**: Tries both HTTPS and HTTP with multiple URL patterns
- **Whitelist & Blacklist**: Supports both allowed file lists and prohibited file detection
- **Instant Protection**: Automatically disconnects from servers when security violations are detected

### 🎯 **Client-Side Only**
- **No Server Mod Required**: Works with any server - no server-side installation needed
- **Zero Server Impact**: Doesn't affect server performance or require admin permissions
- **Universal Compatibility**: Compatible with all Minecraft servers running version 1.20.1

### 🌍 **International Support**
- **Multi-Language**: Full support for English and German languages
- **Localized Messages**: All user interface text is properly translated
- **Expandable**: Easy to add additional language support

### ⚙️ **Extensive Configuration**
- **Granular Control**: Configure every aspect of the mod's behavior
- **Debug Mode**: Optional verbose logging and detailed chat output
- **Flexible Display**: Customizable hash display formats and chat notifications
- **Performance Tuning**: Adjustable timeouts and connection settings

---

## 📥 **Installation**

### **Requirements**
- **Minecraft**: 1.20.1
- **Mod Loader**: Minecraft Forge 47.2.0 or later
- **Java**: Java 17 or higher

### **Installation Steps**
1. Download the latest release from CurseForge or Modrinth
2. Place the `.jar` file in your `.minecraft/mods` folder
3. Launch Minecraft with Forge 1.20.1
4. The mod will automatically create its configuration file on first run

---

## 🖥️ **For Server Administrators**

### **Quick Setup Guide**

**Step 1: Generate Hash File**
Use the included HTML hash generator tool to create your `security-hashes.md` file:
- Open `hash-generator.html` in any web browser
- Drag and drop your server's mod folder
- Generate and download the hash file

**Step 2: Host the File**
Place `security-hashes.md` on your web server at one of these locations:
- `https://your-server.com/security-hashes.md` *(recommended)*
- `https://your-server.com/security/security-hashes.md`
- `https://your-server.com/minecraft/security-hashes.md`
- `https://your-server.com/mods/security-hashes.md`

**Step 3: Test**
Players with the mod will automatically validate their files when connecting!

### **Hash File Format**
```markdown
# Whitelist
- 1a2b3c4d5e6f7890abcdef1234567890abcdef1234567890abcdef1234567890
- 2b3c4d5e6f7890abcdef1234567890abcdef1234567890abcdef1234567890ab

# Blacklist
- 9z8y7x6w5v4u3t2s1r0q9p8o7n6m5l4k3j2i1h0g9f8e7d6c5b4a3z2y1x0w9v
```

---

## 🎮 **For Players**

### **Singleplayer Mode**
- Join any singleplayer world to see your file hashes displayed in chat
- Perfect for verifying your mod installations and detecting file corruption
- Can be disabled in configuration if you prefer quiet operation

### **Multiplayer Mode**
- Automatically validates your files against server security policies
- Receives clear notifications about validation results
- Safely disconnects if prohibited files are detected
- No manual configuration required - works automatically

### **Configuration**
The mod creates a configuration file at `.minecraft/config/sarossecuritycheckermod-client.toml`:

```toml
[general]
enableSingleplayerScanning = true
enableMultiplayerValidation = true
enableDebugMode = false  # Set to true for verbose output

[server]
autoDetectServerHashFile = true
autoDisconnectOnViolation = true

[display]
showChatNotifications = true
maxFilesToDisplay = 50
```

---

## 🔧 **Advanced Features**

### **Debug Mode**
Enable `enableDebugMode = true` for:
- Detailed logging information
- Comprehensive chat output in singleplayer
- Verbose server validation messages
- Perfect for troubleshooting connection issues

### **Custom Server URLs**
Override automatic detection by setting:
```toml
defaultHashListUrl = "https://your-custom-url.com/hashes.md"
```

### **Performance Optimization**
- Configurable connection timeouts
- Adjustable file display limits
- Optional success message suppression
- Efficient multi-threaded file scanning

---

## 🛠️ **Tools Included**

### **Hash Generator (hash-generator.html)**
Professional-grade standalone tool for server administrators:
- **Drag & Drop Interface**: Easy file and folder selection
- **Real-time Processing**: Live progress updates and validation
- **Multiple Formats**: Generate whitelist, blacklist, or both
- **Offline Operation**: No internet connection required
- **Mobile Friendly**: Works on all devices and browsers

---

## 🔗 **Links & Support**

- **Documentation**: Complete setup guides and troubleshooting
- **Source Code**: Open source development on GitHub
- **Issue Tracker**: Report bugs and request features
- **Discord**: Community support and discussions

---

## 📊 **Compatibility**

### **Tested With**
- ✅ Forge 47.2.0+
- ✅ OptiFine
- ✅ JEI (Just Enough Items)
- ✅ Biomes O' Plenty
- ✅ JourneyMap
- ✅ Most popular mod packs

### **Server Compatibility**
- ✅ Vanilla Minecraft servers
- ✅ Forge servers
- ✅ Paper/Spigot servers
- ✅ Modded servers
- ✅ Any server hosting static files

---

## 🏆 **Why Choose Saro's Security Checker?**

### **For Server Owners**
- **Enhanced Security**: Prevent malicious mods and unauthorized modifications
- **Easy Implementation**: No server-side mod installation required
- **Professional Tools**: Included hash generator for easy setup
- **Zero Maintenance**: Static file hosting with automatic client detection

### **For Players**
- **Peace of Mind**: Know your files are verified and secure
- **Automatic Operation**: Works seamlessly without manual intervention
- **Clear Feedback**: Understand exactly what's happening with your files
- **Performance Friendly**: Minimal impact on game performance

### **For Developers**
- **Open Source**: Learn from and contribute to the codebase
- **Modern Architecture**: Built with Minecraft Forge 1.20.1 best practices
- **Extensible Design**: Easy to modify and extend functionality
- **Comprehensive Documentation**: Well-documented code and APIs

---

**Download now and secure your Minecraft experience!**

*Saro's Security Checker Mod - Where security meets simplicity.*
