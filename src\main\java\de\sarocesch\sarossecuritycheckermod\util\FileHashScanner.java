package de.sarocesch.sarossecuritycheckermod.util;

import com.mojang.logging.LogUtils;
import de.sarocesch.sarossecuritycheckermod.config.SecurityConfig;
import net.minecraft.client.Minecraft;
import org.slf4j.Logger;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

public class FileHashScanner {
    private static final Logger LOGGER = LogUtils.getLogger();
    
    public static class FileHashInfo {
        public final String fileName;
        public final String filePath;
        public final String hash;
        public final String type; // "mod" or "resourcepack"
        
        public FileHashInfo(String fileName, String filePath, String hash, String type) {
            this.fileName = fileName;
            this.filePath = filePath;
            this.hash = hash;
            this.type = type;
        }
        
        @Override
        public String toString() {
            return String.format("[%s] %s: %s", type.toUpperCase(), fileName, hash);
        }
    }
    
    /**
     * Scans both mods and resourcepacks folders and returns hash information for all files
     */
    public static List<FileHashInfo> scanAllFiles() {
        List<FileHashInfo> allHashes = new ArrayList<>();
        
        // Get the Minecraft directory
        File minecraftDir = Minecraft.getInstance().gameDirectory;
        
        // Scan mods folder
        File modsDir = new File(minecraftDir, "mods");
        if (modsDir.exists() && modsDir.isDirectory()) {
            LOGGER.info("Scanning mods directory: {}", modsDir.getAbsolutePath());
            allHashes.addAll(scanDirectory(modsDir, "mod"));
        } else {
            LOGGER.warn("Mods directory not found: {}", modsDir.getAbsolutePath());
        }
        
        // Scan resourcepacks folder
        File resourcePacksDir = new File(minecraftDir, "resourcepacks");
        if (resourcePacksDir.exists() && resourcePacksDir.isDirectory()) {
            LOGGER.info("Scanning resourcepacks directory: {}", resourcePacksDir.getAbsolutePath());
            allHashes.addAll(scanDirectory(resourcePacksDir, "resourcepack"));
        } else {
            LOGGER.warn("Resourcepacks directory not found: {}", resourcePacksDir.getAbsolutePath());
        }
        
        return allHashes;
    }
    
    /**
     * Scans a directory recursively and generates hashes for all files
     */
    private static List<FileHashInfo> scanDirectory(File directory, String type) {
        List<FileHashInfo> hashes = new ArrayList<>();
        
        try (Stream<Path> paths = Files.walk(directory.toPath())) {
            paths.filter(Files::isRegularFile)
                 .forEach(path -> {
                     try {
                         String hash = calculateSHA256(path.toFile());
                         String fileName = path.getFileName().toString();
                         String relativePath = directory.toPath().relativize(path).toString();
                         
                         FileHashInfo info = new FileHashInfo(fileName, relativePath, hash, type);
                         hashes.add(info);
                         
                         LOGGER.debug("Generated hash for {}: {}", relativePath, hash);
                     } catch (Exception e) {
                         LOGGER.error("Failed to generate hash for file: {}", path, e);
                     }
                 });
        } catch (IOException e) {
            LOGGER.error("Failed to scan directory: {}", directory.getAbsolutePath(), e);
        }
        
        return hashes;
    }
    
    /**
     * Calculates SHA-256 hash for a file
     */
    private static String calculateSHA256(File file) throws IOException, NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            
            while ((bytesRead = fis.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
            }
        }
        
        byte[] hashBytes = digest.digest();
        StringBuilder hexString = new StringBuilder();
        
        for (byte b : hashBytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        
        return hexString.toString();
    }
    
    /**
     * Groups file hash information by type for easier processing
     */
    public static Map<String, List<FileHashInfo>> groupByType(List<FileHashInfo> hashes) {
        Map<String, List<FileHashInfo>> grouped = new HashMap<>();
        
        for (FileHashInfo hash : hashes) {
            grouped.computeIfAbsent(hash.type, k -> new ArrayList<>()).add(hash);
        }
        
        return grouped;
    }
    
    /**
     * Formats hash information for display in chat
     */
    public static List<String> formatForChat(List<FileHashInfo> hashes) {
        List<String> chatMessages = new ArrayList<>();

        Map<String, List<FileHashInfo>> grouped = groupByType(hashes);

        // Add mods section
        if (grouped.containsKey("mod")) {
            chatMessages.add("§6=== MOD FILES ===");
            for (FileHashInfo hash : grouped.get("mod")) {
                String formattedHash = SecurityConfig.formatHashForDisplay(hash.hash);
                chatMessages.add("§e" + hash.fileName + "§r: §7" + formattedHash);
            }
            chatMessages.add("");
        }

        // Add resourcepacks section
        if (grouped.containsKey("resourcepack")) {
            chatMessages.add("§6=== RESOURCE PACK FILES ===");
            for (FileHashInfo hash : grouped.get("resourcepack")) {
                String formattedHash = SecurityConfig.formatHashForDisplay(hash.hash);
                chatMessages.add("§e" + hash.fileName + "§r: §7" + formattedHash);
            }
        }

        return chatMessages;
    }
}
