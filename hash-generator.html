<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Saro's Security Checker - Hash Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .section {
            margin-bottom: 40px;
        }

        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8em;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }

        .drop-zone {
            border: 3px dashed #3498db;
            border-radius: 10px;
            padding: 60px 20px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .drop-zone:hover, .drop-zone.dragover {
            border-color: #2980b9;
            background: #e3f2fd;
            transform: translateY(-2px);
        }

        .drop-zone i {
            font-size: 4em;
            color: #3498db;
            margin-bottom: 20px;
        }

        .drop-zone p {
            font-size: 1.2em;
            color: #555;
            margin-bottom: 10px;
        }

        .drop-zone small {
            color: #777;
        }

        .file-input {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
        }

        .btn-success:hover {
            box-shadow: 0 5px 15px rgba(46, 204, 113, 0.4);
        }

        .options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .option-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
        }

        .option-group h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .checkbox-group {
            margin-bottom: 15px;
        }

        .checkbox-group label {
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            margin-right: 10px;
            transform: scale(1.2);
        }

        .progress-container {
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            display: none;
        }

        .progress-bar {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            height: 30px;
            width: 0%;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .file-list {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            max-height: 300px;
            overflow-y: auto;
            display: none;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #e0e0e0;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-name {
            font-weight: bold;
            color: #2c3e50;
        }

        .file-hash {
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            color: #666;
            word-break: break-all;
        }

        .output-container {
            background: #2c3e50;
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            display: none;
        }

        .output-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .output-content {
            background: #34495e;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }

        @media (max-width: 768px) {
            .main-content {
                padding: 20px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ Saro's Security Checker</h1>
            <p>Hash Generator Tool for Minecraft Server Administrators</p>
        </div>

        <div class="main-content">
            <div class="alert alert-info">
                <strong>Instructions:</strong> Select or drag-and-drop your mods folder, resource packs folder, or any directory containing files you want to generate hashes for. This tool will create a properly formatted <code>security-hashes.md</code> file for your server.
            </div>

            <div class="section">
                <h2>📁 Select Files</h2>
                <div class="drop-zone" id="dropZone">
                    <div style="font-size: 4em; margin-bottom: 20px;">📂</div>
                    <p><strong>Drop files or folders here</strong></p>
                    <p>or</p>
                    <button class="btn" onclick="document.getElementById('fileInput').click()">Browse Files</button>
                    <button class="btn" onclick="document.getElementById('folderInput').click()">Browse Folder</button>
                    <br><small>Supports: .jar, .zip, .png, and all other file types</small>
                </div>
                
                <input type="file" id="fileInput" class="file-input" multiple accept="*/*">
                <input type="file" id="folderInput" class="file-input" webkitdirectory multiple>
            </div>

            <div class="section">
                <h2>⚙️ Options</h2>
                <div class="options">
                    <div class="option-group">
                        <h3>Output Format</h3>
                        <div class="checkbox-group">
                            <label>
                                <input type="checkbox" id="includeWhitelist" checked>
                                Generate Whitelist Section
                            </label>
                            <label>
                                <input type="checkbox" id="includeBlacklist">
                                Generate Blacklist Section
                            </label>
                        </div>
                    </div>
                    
                    <div class="option-group">
                        <h3>File Filtering</h3>
                        <div class="checkbox-group">
                            <label>
                                <input type="checkbox" id="includeJars" checked>
                                Include .jar files (mods)
                            </label>
                            <label>
                                <input type="checkbox" id="includeZips" checked>
                                Include .zip files (resource packs)
                            </label>
                            <label>
                                <input type="checkbox" id="includeOthers" checked>
                                Include other file types
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="progress-container" id="progressContainer">
                <div class="progress-bar" id="progressBar">0%</div>
            </div>

            <div class="file-list" id="fileList">
                <h3>📋 Processed Files</h3>
                <div id="fileListContent"></div>
            </div>

            <div class="section">
                <button class="btn btn-success" id="generateBtn" onclick="generateHashes()" disabled>
                    🔐 Generate Hash File
                </button>
                <button class="btn" id="clearBtn" onclick="clearFiles()">
                    🗑️ Clear Files
                </button>
            </div>

            <div class="output-container" id="outputContainer">
                <div class="output-header">
                    <h3>📄 Generated security-hashes.md</h3>
                    <button class="btn" onclick="downloadFile()">💾 Download File</button>
                </div>
                <div class="output-content" id="outputContent"></div>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        let processedHashes = [];

        // Drag and drop functionality
        const dropZone = document.getElementById('dropZone');
        const fileInput = document.getElementById('fileInput');
        const folderInput = document.getElementById('folderInput');

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('dragover');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('dragover');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            handleFiles(e.dataTransfer.files);
        });

        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        folderInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            selectedFiles = Array.from(files);
            updateFileList();
            document.getElementById('generateBtn').disabled = selectedFiles.length === 0;
        }

        function updateFileList() {
            const fileListContainer = document.getElementById('fileList');
            const fileListContent = document.getElementById('fileListContent');
            
            if (selectedFiles.length === 0) {
                fileListContainer.style.display = 'none';
                return;
            }

            fileListContainer.style.display = 'block';
            fileListContent.innerHTML = '';

            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.innerHTML = `
                    <div>
                        <div class="file-name">${file.name}</div>
                        <small>Size: ${formatFileSize(file.size)} | Type: ${file.type || 'Unknown'}</small>
                    </div>
                    <div style="color: #999;">Pending...</div>
                `;
                fileListContent.appendChild(fileItem);
            });
        }

        async function generateHashes() {
            if (selectedFiles.length === 0) return;

            const progressContainer = document.getElementById('progressContainer');
            const progressBar = document.getElementById('progressBar');
            const generateBtn = document.getElementById('generateBtn');
            
            progressContainer.style.display = 'block';
            generateBtn.disabled = true;
            processedHashes = [];

            const includeJars = document.getElementById('includeJars').checked;
            const includeZips = document.getElementById('includeZips').checked;
            const includeOthers = document.getElementById('includeOthers').checked;

            let processedCount = 0;
            const totalFiles = selectedFiles.length;

            for (let i = 0; i < selectedFiles.length; i++) {
                const file = selectedFiles[i];
                
                // Filter files based on options
                const isJar = file.name.toLowerCase().endsWith('.jar');
                const isZip = file.name.toLowerCase().endsWith('.zip');
                const isOther = !isJar && !isZip;

                if ((isJar && !includeJars) || (isZip && !includeZips) || (isOther && !includeOthers)) {
                    processedCount++;
                    updateProgress(processedCount, totalFiles);
                    continue;
                }

                try {
                    const hash = await calculateSHA256(file);
                    processedHashes.push({
                        name: file.name,
                        hash: hash,
                        size: file.size,
                        type: getFileType(file.name)
                    });
                    
                    // Update file list item
                    const fileItems = document.querySelectorAll('.file-item');
                    if (fileItems[i]) {
                        const statusDiv = fileItems[i].querySelector('div:last-child');
                        statusDiv.innerHTML = `<span style="color: #27ae60;">✓ Complete</span>`;
                    }
                } catch (error) {
                    console.error('Error processing file:', file.name, error);
                    const fileItems = document.querySelectorAll('.file-item');
                    if (fileItems[i]) {
                        const statusDiv = fileItems[i].querySelector('div:last-child');
                        statusDiv.innerHTML = `<span style="color: #e74c3c;">✗ Error</span>`;
                    }
                }

                processedCount++;
                updateProgress(processedCount, totalFiles);
            }

            generateOutput();
            generateBtn.disabled = false;
        }

        function updateProgress(current, total) {
            const percentage = Math.round((current / total) * 100);
            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = percentage + '%';
            progressBar.textContent = percentage + '%';
        }

        async function calculateSHA256(file) {
            const buffer = await file.arrayBuffer();
            const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        }

        function getFileType(filename) {
            const ext = filename.toLowerCase().split('.').pop();
            switch (ext) {
                case 'jar': return 'mod';
                case 'zip': return 'resourcepack';
                default: return 'file';
            }
        }

        function generateOutput() {
            const includeWhitelist = document.getElementById('includeWhitelist').checked;
            const includeBlacklist = document.getElementById('includeBlacklist').checked;
            
            let output = '# Saro\'s Security Checker - Hash File\n';
            output += '# Generated on: ' + new Date().toISOString() + '\n';
            output += '# Total files: ' + processedHashes.length + '\n\n';

            if (includeWhitelist) {
                output += '# Whitelist\n';
                output += '# Add file hashes that are ALLOWED on the server\n';
                processedHashes.forEach(item => {
                    output += `- ${item.hash}  # ${item.name} (${item.type})\n`;
                });
                output += '\n';
            }

            if (includeBlacklist) {
                output += '# Blacklist\n';
                output += '# Add file hashes that are PROHIBITED on the server\n';
                output += '# Example entries (remove these and add your own):\n';
                output += '# - 1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef  # malicious-mod.jar\n';
                output += '# - abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890  # banned-resource-pack.zip\n\n';
            }

            const outputContainer = document.getElementById('outputContainer');
            const outputContent = document.getElementById('outputContent');
            
            outputContainer.style.display = 'block';
            outputContent.textContent = output;
        }

        function downloadFile() {
            const content = document.getElementById('outputContent').textContent;
            const blob = new Blob([content], { type: 'text/markdown' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = 'security-hashes.md';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function clearFiles() {
            selectedFiles = [];
            processedHashes = [];
            document.getElementById('fileList').style.display = 'none';
            document.getElementById('progressContainer').style.display = 'none';
            document.getElementById('outputContainer').style.display = 'none';
            document.getElementById('generateBtn').disabled = true;
            document.getElementById('fileInput').value = '';
            document.getElementById('folderInput').value = '';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        // Additional utility functions
        function copyToClipboard() {
            const content = document.getElementById('outputContent').textContent;
            navigator.clipboard.writeText(content).then(() => {
                alert('Hash file content copied to clipboard!');
            }).catch(err => {
                console.error('Failed to copy to clipboard:', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = content;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('Hash file content copied to clipboard!');
            });
        }

        // Add copy button to output header
        document.addEventListener('DOMContentLoaded', function() {
            const outputHeader = document.querySelector('.output-header');
            if (outputHeader) {
                const copyBtn = document.createElement('button');
                copyBtn.className = 'btn';
                copyBtn.innerHTML = '📋 Copy to Clipboard';
                copyBtn.onclick = copyToClipboard;
                outputHeader.appendChild(copyBtn);
            }
        });

        // Validate file types and show warnings
        function validateFiles() {
            const hasJars = selectedFiles.some(f => f.name.toLowerCase().endsWith('.jar'));
            const hasZips = selectedFiles.some(f => f.name.toLowerCase().endsWith('.zip'));

            if (!hasJars && !hasZips) {
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-warning';
                alertDiv.innerHTML = '<strong>Notice:</strong> No .jar (mod) or .zip (resource pack) files detected. Make sure you\'ve selected the correct folder containing your Minecraft mods or resource packs.';

                const mainContent = document.querySelector('.main-content');
                const firstSection = mainContent.querySelector('.section');
                mainContent.insertBefore(alertDiv, firstSection);

                setTimeout(() => alertDiv.remove(), 10000);
            }
        }

        // Enhanced file handling with validation
        function handleFilesEnhanced(files) {
            handleFiles(files);
            validateFiles();
        }

        // Update event listeners to use enhanced handling
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('dragover');
            handleFilesEnhanced(e.dataTransfer.files);
        });

        fileInput.addEventListener('change', (e) => {
            handleFilesEnhanced(e.target.files);
        });

        folderInput.addEventListener('change', (e) => {
            handleFilesEnhanced(e.target.files);
        });
    </script>
</body>
</html>
