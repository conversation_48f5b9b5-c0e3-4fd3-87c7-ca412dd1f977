{"sarossecuritychecker.message.singleplayer.header": "§6=== SARO'S SECURITY CHECKER - FILE HASHES ===", "sarossecuritychecker.message.singleplayer.footer": "§6=== SCAN COMPLETE - %d FILES SCANNED ===", "sarossecuritychecker.message.singleplayer.limit_reached": "§7... and %d more files (limit reached)", "sarossecuritychecker.message.multiplayer.validation_passed": "§aServer security validation passed - %s", "sarossecuritychecker.message.multiplayer.auto_detection": "Attempting automatic server hash file detection...", "sarossecuritychecker.message.multiplayer.auto_detection_failed": "Auto-detection failed, trying configured URL: %s", "sarossecuritychecker.message.multiplayer.no_hash_lists": "No server hash lists found - skipping validation", "sarossecuritychecker.message.multiplayer.validation_complete": "Validation complete: %s", "sarossecuritychecker.message.security.violation_detected": "Security Validation Failed!", "sarossecuritychecker.message.security.validation_summary": "Validation Summary: %s", "sarossecuritychecker.message.security.files_not_allowed": "The following files are not allowed by this server:", "sarossecuritychecker.message.security.remove_files": "Please remove the prohibited files and reconnect.", "sarossecuritychecker.message.security.disconnect_reason": "Security Validation Failed", "sarossecuritychecker.message.security.warning": "§cSecurity Validation Warning!", "sarossecuritychecker.message.security.auto_disconnect_disabled": "§fConsider removing these files. Auto-disconnect is disabled in configuration.", "sarossecuritychecker.message.file.blacklisted": "File '%s' (hash: %s) is blacklisted by the server", "sarossecuritychecker.message.file.not_whitelisted": "File '%s' (hash: %s) is not in the server's whitelist", "sarossecuritychecker.message.file.validation_failed": "File '%s' (hash: %s) failed validation", "sarossecuritychecker.message.file.blacklisted_detailed": "BLACKLISTED: %s\n  File: %s\n  Hash: %s\n  Type: %s", "sarossecuritychecker.message.file.not_whitelisted_detailed": "NOT WHITELISTED: %s\n  File: %s\n  Hash: %s\n  Type: %s", "sarossecuritychecker.message.file.validation_failed_detailed": "VALIDATION FAILED: %s\n  File: %s\n  Hash: %s\n  Type: %s", "sarossecuritychecker.message.stats.summary": "Total: %d, Allowed: %d, Blacklisted: %d, Unknown: %d", "sarossecuritychecker.message.config.singleplayer_disabled": "Singleplayer scanning is disabled in configuration", "sarossecuritychecker.message.config.multiplayer_disabled": "Multiplayer validation is disabled in configuration", "sarossecuritychecker.message.log.scan_started": "Starting file hash scan...", "sarossecuritychecker.message.log.scan_completed": "File hash scan completed. %d files scanned.", "sarossecuritychecker.message.log.validation_started": "Starting server hash validation...", "sarossecuritychecker.message.log.validation_failed": "Server validation failed - %d files violated security policy", "sarossecuritychecker.message.log.validation_passed": "All files passed server validation", "sarossecuritychecker.message.log.auto_detection_disabled": "Auto-detection of server hash files is disabled", "sarossecuritychecker.message.log.server_address_unknown": "Could not determine current server address for hash file detection", "sarossecuritychecker.message.log.hash_file_found": "Successfully found hash validation file at: %s", "sarossecuritychecker.message.log.hash_file_not_found": "No hash validation file found for server: %s", "sarossecuritychecker.message.log.violations_detected": "Security violations detected but auto-disconnect is disabled", "sarossecuritychecker.message.error.scan_failed": "§cError: Failed to scan files for hashes. Check logs for details.", "sarossecuritychecker.message.error.validation_failed": "§cError: Failed to validate files against server. Check logs for details.", "sarossecuritychecker.message.debug.mode_disabled": "Debug mode is disabled - chat output suppressed", "sarossecuritychecker.message.debug.quiet_scan": "File scan completed silently in non-debug mode", "sarossecuritychecker.message.debug.verbose_logging": "Debug mode enabled - showing verbose output"}