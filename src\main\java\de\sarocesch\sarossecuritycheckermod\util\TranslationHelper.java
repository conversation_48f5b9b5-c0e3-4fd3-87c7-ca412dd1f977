package de.sarocesch.sarossecuritycheckermod.util;

import net.minecraft.network.chat.Component;

public class TranslationHelper {
    
    // Singleplayer messages
    public static Component getSingleplayerHeader() {
        return Component.translatable("sarossecuritychecker.message.singleplayer.header");
    }
    
    public static Component getSingleplayerFooter(int fileCount) {
        return Component.translatable("sarossecuritychecker.message.singleplayer.footer", fileCount);
    }
    
    public static Component getSingleplayerLimitReached(int remainingFiles) {
        return Component.translatable("sarossecuritychecker.message.singleplayer.limit_reached", remainingFiles);
    }
    
    // Multiplayer messages
    public static Component getMultiplayerValidationPassed(String summary) {
        return Component.translatable("sarossecuritychecker.message.multiplayer.validation_passed", summary);
    }
    
    public static String getMultiplayerAutoDetection() {
        return Component.translatable("sarossecuritychecker.message.multiplayer.auto_detection").getString();
    }
    
    public static String getMultiplayerAutoDetectionFailed(String url) {
        return Component.translatable("sarossecuritychecker.message.multiplayer.auto_detection_failed", url).getString();
    }
    
    public static String getMultiplayerNoHashLists() {
        return Component.translatable("sarossecuritychecker.message.multiplayer.no_hash_lists").getString();
    }
    
    public static String getMultiplayerValidationComplete(String summary) {
        return Component.translatable("sarossecuritychecker.message.multiplayer.validation_complete", summary).getString();
    }
    
    // Security messages
    public static String getSecurityViolationDetected() {
        return Component.translatable("sarossecuritychecker.message.security.violation_detected").getString();
    }
    
    public static String getSecurityValidationSummary(String summary) {
        return Component.translatable("sarossecuritychecker.message.security.validation_summary", summary).getString();
    }
    
    public static String getSecurityFilesNotAllowed() {
        return Component.translatable("sarossecuritychecker.message.security.files_not_allowed").getString();
    }
    
    public static String getSecurityRemoveFiles() {
        return Component.translatable("sarossecuritychecker.message.security.remove_files").getString();
    }
    
    public static Component getSecurityDisconnectReason() {
        return Component.translatable("sarossecuritychecker.message.security.disconnect_reason");
    }
    
    public static Component getSecurityWarning() {
        return Component.translatable("sarossecuritychecker.message.security.warning");
    }
    
    public static String getSecurityAutoDisconnectDisabled() {
        return Component.translatable("sarossecuritychecker.message.security.auto_disconnect_disabled").getString();
    }
    
    // File validation messages
    public static String getFileBlacklisted(String fileName, String hashPreview) {
        return Component.translatable("sarossecuritychecker.message.file.blacklisted", fileName, hashPreview).getString();
    }
    
    public static String getFileNotWhitelisted(String fileName, String hashPreview) {
        return Component.translatable("sarossecuritychecker.message.file.not_whitelisted", fileName, hashPreview).getString();
    }
    
    public static String getFileValidationFailed(String fileName, String hashPreview) {
        return Component.translatable("sarossecuritychecker.message.file.validation_failed", fileName, hashPreview).getString();
    }
    
    public static String getFileBlacklistedDetailed(String fileName, String filePath, String hash, String type) {
        return Component.translatable("sarossecuritychecker.message.file.blacklisted_detailed", fileName, filePath, hash, type).getString();
    }
    
    public static String getFileNotWhitelistedDetailed(String fileName, String filePath, String hash, String type) {
        return Component.translatable("sarossecuritychecker.message.file.not_whitelisted_detailed", fileName, filePath, hash, type).getString();
    }
    
    public static String getFileValidationFailedDetailed(String fileName, String filePath, String hash, String type) {
        return Component.translatable("sarossecuritychecker.message.file.validation_failed_detailed", fileName, filePath, hash, type).getString();
    }
    
    // Statistics messages
    public static String getStatsSummary(int total, int allowed, int blacklisted, int unknown) {
        return Component.translatable("sarossecuritychecker.message.stats.summary", total, allowed, blacklisted, unknown).getString();
    }
    
    // Configuration messages
    public static String getConfigSingleplayerDisabled() {
        return Component.translatable("sarossecuritychecker.message.config.singleplayer_disabled").getString();
    }
    
    public static String getConfigMultiplayerDisabled() {
        return Component.translatable("sarossecuritychecker.message.config.multiplayer_disabled").getString();
    }
    
    // Log messages
    public static String getLogScanStarted() {
        return Component.translatable("sarossecuritychecker.message.log.scan_started").getString();
    }
    
    public static String getLogScanCompleted(int fileCount) {
        return Component.translatable("sarossecuritychecker.message.log.scan_completed", fileCount).getString();
    }
    
    public static String getLogValidationStarted() {
        return Component.translatable("sarossecuritychecker.message.log.validation_started").getString();
    }
    
    public static String getLogValidationFailed(int violationCount) {
        return Component.translatable("sarossecuritychecker.message.log.validation_failed", violationCount).getString();
    }
    
    public static String getLogValidationPassed() {
        return Component.translatable("sarossecuritychecker.message.log.validation_passed").getString();
    }
    
    public static String getLogAutoDetectionDisabled() {
        return Component.translatable("sarossecuritychecker.message.log.auto_detection_disabled").getString();
    }
    
    public static String getLogServerAddressUnknown() {
        return Component.translatable("sarossecuritychecker.message.log.server_address_unknown").getString();
    }
    
    public static String getLogHashFileFound(String url) {
        return Component.translatable("sarossecuritychecker.message.log.hash_file_found", url).getString();
    }
    
    public static String getLogHashFileNotFound(String serverAddress) {
        return Component.translatable("sarossecuritychecker.message.log.hash_file_not_found", serverAddress).getString();
    }
    
    public static String getLogViolationsDetected() {
        return Component.translatable("sarossecuritychecker.message.log.violations_detected").getString();
    }
    
    // Error messages
    public static Component getErrorScanFailed() {
        return Component.translatable("sarossecuritychecker.message.error.scan_failed");
    }

    public static Component getErrorValidationFailed() {
        return Component.translatable("sarossecuritychecker.message.error.validation_failed");
    }

    // Debug messages
    public static String getDebugModeDisabled() {
        return Component.translatable("sarossecuritychecker.message.debug.mode_disabled").getString();
    }

    public static String getDebugQuietScan() {
        return Component.translatable("sarossecuritychecker.message.debug.quiet_scan").getString();
    }

    public static String getDebugVerboseLogging() {
        return Component.translatable("sarossecuritychecker.message.debug.verbose_logging").getString();
    }
}
