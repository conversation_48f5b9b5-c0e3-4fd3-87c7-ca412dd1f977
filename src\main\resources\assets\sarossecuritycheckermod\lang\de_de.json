{"sarossecuritychecker.message.singleplayer.header": "§6=== SARO'S SICHERHEITSPRÜFER - DATEI-HASHES ===", "sarossecuritychecker.message.singleplayer.footer": "§6=== SCAN ABGESCHLOSSEN - %d DATEIEN GESCANNT ===", "sarossecuritychecker.message.singleplayer.limit_reached": "§7... und %d weitere Dateien (Limit erreicht)", "sarossecuritychecker.message.multiplayer.validation_passed": "§aServer-Sicherheitsvalidierung bestanden - %s", "sarossecuritychecker.message.multiplayer.auto_detection": "Versuche automatische Erkennung der Server-Hash-Datei...", "sarossecuritychecker.message.multiplayer.auto_detection_failed": "Automatische Erkennung fehlgeschlagen, versuche konfigurierte URL: %s", "sarossecuritychecker.message.multiplayer.no_hash_lists": "<PERSON><PERSON>-Hash-Listen gefunden - Validierung wird übersprungen", "sarossecuritychecker.message.multiplayer.validation_complete": "Validierung abgeschlossen: %s", "sarossecuritychecker.message.security.violation_detected": "Sicherheitsvalidierung fehlgeschlagen!", "sarossecuritychecker.message.security.validation_summary": "Validierungszusammenfassung: %s", "sarossecuritychecker.message.security.files_not_allowed": "Die folgenden Dateien sind auf diesem Server nicht erlaubt:", "sarossecuritychecker.message.security.remove_files": "Bitte entfernen Sie die verbotenen Dateien und verbinden Sie sich erneut.", "sarossecuritychecker.message.security.disconnect_reason": "Sicherheitsvalidierung fehlgeschlagen", "sarossecuritychecker.message.security.warning": "§cSicherheitsvalidierung Warnung!", "sarossecuritychecker.message.security.auto_disconnect_disabled": "§fErwägen Si<PERSON>, diese Dateien zu entfernen. Auto-Trennung ist in der Konfiguration deaktiviert.", "sarossecuritychecker.message.file.blacklisted": "Datei '%s' (Hash: %s) ist vom Server auf der schwarzen Liste", "sarossecuritychecker.message.file.not_whitelisted": "Datei '%s' (Hash: %s) ist nicht in der Whitelist des Servers", "sarossecuritychecker.message.file.validation_failed": "Datei '%s' (Hash: %s) Validierung fehlgeschlagen", "sarossecuritychecker.message.file.blacklisted_detailed": "BLACKLISTED: %s\n  Datei: %s\n  Hash: %s\n  Typ: %s", "sarossecuritychecker.message.file.not_whitelisted_detailed": "NICHT WHITELISTED: %s\n  Datei: %s\n  Hash: %s\n  Typ: %s", "sarossecuritychecker.message.file.validation_failed_detailed": "VALIDIERUNG FEHLGESCHLAGEN: %s\n  Datei: %s\n  Hash: %s\n  Typ: %s", "sarossecuritychecker.message.stats.summary": "Gesamt: %d, Erl<PERSON>bt: %d, Blacklisted: %d, Unbekannt: %d", "sarossecuritychecker.message.config.singleplayer_disabled": "Einzelspieler-Scanning ist in der Konfiguration deaktiviert", "sarossecuritychecker.message.config.multiplayer_disabled": "Mehrspieler-Validierung ist in der Konfiguration deaktiviert", "sarossecuritychecker.message.log.scan_started": "<PERSON><PERSON>-<PERSON>h-<PERSON>an...", "sarossecuritychecker.message.log.scan_completed": "Datei-Hash-Scan abgeschlossen. %d <PERSON><PERSON> gescannt.", "sarossecuritychecker.message.log.validation_started": "Starte Server-Hash-Validierung...", "sarossecuritychecker.message.log.validation_failed": "Server-Validierung fehlgeschlagen - %d Dateien verletzten die Sicherheitsrichtlinie", "sarossecuritychecker.message.log.validation_passed": "Alle Dateien haben die Server-Validierung bestanden", "sarossecuritychecker.message.log.auto_detection_disabled": "Automatische Erkennung von Server-Hash-Dateien ist deaktiviert", "sarossecuritychecker.message.log.server_address_unknown": "Konnte aktuelle Server-Adresse für Hash-Datei-Erkennung nicht bestimmen", "sarossecuritychecker.message.log.hash_file_found": "Hash-Validierungsdatei erfolgreich gefunden unter: %s", "sarossecuritychecker.message.log.hash_file_not_found": "<PERSON><PERSON>-Validierungsdatei für Server gefunden: %s", "sarossecuritychecker.message.log.violations_detected": "Sicherheitsverletzungen erkannt, aber Auto-Trennung ist deaktiviert", "sarossecuritychecker.message.error.scan_failed": "§cFehler: <PERSON><PERSON> konnten nicht für Hashes gescannt werden. Prüfen Sie die Logs für Details.", "sarossecuritychecker.message.error.validation_failed": "§cFehler: <PERSON><PERSON> konnten nicht gegen Server validiert werden. Prüfen Sie die Logs für Details.", "sarossecuritychecker.message.debug.mode_disabled": "Debug-Modus ist deaktiviert - Chat-Ausgabe unterdrückt", "sarossecuritychecker.message.debug.quiet_scan": "Datei-Scan im Nicht-Debug-Modus still abgeschlossen", "sarossecuritychecker.message.debug.verbose_logging": "Debug-Modus aktiviert - zeige ausführliche Ausgabe"}