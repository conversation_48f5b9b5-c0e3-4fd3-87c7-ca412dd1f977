package de.sarocesch.sarossecuritycheckermod.config;

import net.minecraftforge.common.ForgeConfigSpec;
import net.minecraftforge.fml.common.Mod;

@Mod.EventBusSubscriber
public class SecurityConfig {
    
    public static final ForgeConfigSpec.Builder BUILDER = new ForgeConfigSpec.Builder();
    public static final ForgeConfigSpec SPEC;
    
    // General settings
    public static final ForgeConfigSpec.BooleanValue ENABLE_SINGLEPLAYER_SCANNING;
    public static final ForgeConfigSpec.BooleanValue ENABLE_MULTIPLAYER_VALIDATION;
    public static final ForgeConfigSpec.BooleanValue SHOW_DETAILED_HASHES;
    
    // Server validation settings
    public static final ForgeConfigSpec.ConfigValue<String> DEFAULT_HASH_LIST_URL;
    public static final ForgeConfigSpec.BooleanValue AUTO_DETECT_SERVER_HASH_FILE;
    public static final ForgeConfigSpec.ConfigValue<String> SERVER_HASH_FILE_NAME;
    public static final ForgeConfigSpec.BooleanValue TRY_HTTP_AND_HTTPS;
    public static final ForgeConfigSpec.BooleanValue AUTO_DISCONNECT_ON_VIOLATION;
    public static final ForgeConfigSpec.IntValue CONNECTION_TIMEOUT_SECONDS;
    
    // Display settings
    public static final ForgeConfigSpec.BooleanValue SHOW_CHAT_NOTIFICATIONS;
    public static final ForgeConfigSpec.BooleanValue SHOW_SUCCESS_MESSAGES;
    public static final ForgeConfigSpec.IntValue MAX_FILES_TO_DISPLAY;
    
    static {
        BUILDER.comment("Saro's Security Checker Mod Configuration")
               .push("general");
        
        ENABLE_SINGLEPLAYER_SCANNING = BUILDER
            .comment("Enable file hash scanning and display in singleplayer worlds")
            .define("enableSingleplayerScanning", true);
        
        ENABLE_MULTIPLAYER_VALIDATION = BUILDER
            .comment("Enable server hash validation in multiplayer")
            .define("enableMultiplayerValidation", true);
        
        SHOW_DETAILED_HASHES = BUILDER
            .comment("Show full SHA-256 hashes in chat (if false, shows shortened hashes)")
            .define("showDetailedHashes", true);
        
        BUILDER.pop();
        
        BUILDER.comment("Server validation settings")
               .push("server");
        
        DEFAULT_HASH_LIST_URL = BUILDER
            .comment("Default URL to fetch server hash lists from (leave empty to disable)")
            .define("defaultHashListUrl", "");

        AUTO_DETECT_SERVER_HASH_FILE = BUILDER
            .comment("Automatically try to detect hash validation files from the server")
            .define("autoDetectServerHashFile", true);

        SERVER_HASH_FILE_NAME = BUILDER
            .comment("Filename to look for when auto-detecting server hash files")
            .define("serverHashFileName", "security-hashes.md");

        TRY_HTTP_AND_HTTPS = BUILDER
            .comment("Try both HTTP and HTTPS when auto-detecting server hash files")
            .define("tryHttpAndHttps", true);

        AUTO_DISCONNECT_ON_VIOLATION = BUILDER
            .comment("Automatically disconnect from server when security violations are detected")
            .define("autoDisconnectOnViolation", true);

        CONNECTION_TIMEOUT_SECONDS = BUILDER
            .comment("Timeout in seconds for HTTP requests to fetch hash lists")
            .defineInRange("connectionTimeoutSeconds", 10, 1, 60);
        
        BUILDER.pop();
        
        BUILDER.comment("Display settings")
               .push("display");
        
        SHOW_CHAT_NOTIFICATIONS = BUILDER
            .comment("Show notifications in chat")
            .define("showChatNotifications", true);
        
        SHOW_SUCCESS_MESSAGES = BUILDER
            .comment("Show success messages when validation passes")
            .define("showSuccessMessages", true);
        
        MAX_FILES_TO_DISPLAY = BUILDER
            .comment("Maximum number of files to display in chat (0 = unlimited)")
            .defineInRange("maxFilesToDisplay", 50, 0, 1000);
        
        BUILDER.pop();
        
        SPEC = BUILDER.build();
    }
    
    /**
     * Gets the configured hash list URL, with fallback logic
     */
    public static String getHashListUrl() {
        String configUrl = DEFAULT_HASH_LIST_URL.get();
        if (configUrl != null && !configUrl.trim().isEmpty()) {
            return configUrl.trim();
        }
        
        // TODO: Could implement server-specific URL resolution here
        // For example: "https://example.com/server-hashes/" + serverAddress + ".md"
        
        return null; // No URL configured
    }
    
    /**
     * Gets the connection timeout in milliseconds
     */
    public static int getConnectionTimeoutMs() {
        return CONNECTION_TIMEOUT_SECONDS.get() * 1000;
    }
    
    /**
     * Checks if singleplayer scanning is enabled
     */
    public static boolean isSingleplayerScanningEnabled() {
        return ENABLE_SINGLEPLAYER_SCANNING.get();
    }
    
    /**
     * Checks if multiplayer validation is enabled
     */
    public static boolean isMultiplayerValidationEnabled() {
        return ENABLE_MULTIPLAYER_VALIDATION.get();
    }
    
    /**
     * Checks if detailed hashes should be shown
     */
    public static boolean shouldShowDetailedHashes() {
        return SHOW_DETAILED_HASHES.get();
    }
    
    /**
     * Checks if auto-disconnect is enabled
     */
    public static boolean shouldAutoDisconnect() {
        return AUTO_DISCONNECT_ON_VIOLATION.get();
    }
    
    /**
     * Checks if chat notifications should be shown
     */
    public static boolean shouldShowChatNotifications() {
        return SHOW_CHAT_NOTIFICATIONS.get();
    }
    
    /**
     * Checks if success messages should be shown
     */
    public static boolean shouldShowSuccessMessages() {
        return SHOW_SUCCESS_MESSAGES.get();
    }
    
    /**
     * Gets the maximum number of files to display
     */
    public static int getMaxFilesToDisplay() {
        return MAX_FILES_TO_DISPLAY.get();
    }
    
    /**
     * Checks if automatic server hash file detection is enabled
     */
    public static boolean shouldAutoDetectServerHashFile() {
        return AUTO_DETECT_SERVER_HASH_FILE.get();
    }

    /**
     * Gets the server hash file name to look for
     */
    public static String getServerHashFileName() {
        return SERVER_HASH_FILE_NAME.get();
    }

    /**
     * Checks if both HTTP and HTTPS should be tried
     */
    public static boolean shouldTryHttpAndHttps() {
        return TRY_HTTP_AND_HTTPS.get();
    }

    /**
     * Formats a hash for display based on configuration
     */
    public static String formatHashForDisplay(String hash) {
        if (shouldShowDetailedHashes()) {
            return hash;
        } else {
            // Show first 8 and last 8 characters with ... in between
            if (hash.length() > 16) {
                return hash.substring(0, 8) + "..." + hash.substring(hash.length() - 8);
            }
            return hash;
        }
    }
}
