package de.sarocesch.sarossecuritycheckermod.util;

import com.mojang.logging.LogUtils;
import de.sarocesch.sarossecuritycheckermod.config.SecurityConfig;
import org.slf4j.Logger;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ServerHashListFetcher {
    private static final Logger LOGGER = LogUtils.getLogger();
    
    // Default timeout for HTTP connections (10 seconds) - will be overridden by config
    private static final int DEFAULT_TIMEOUT = 10000;
    
    public static class HashLists {
        public final Set<String> whitelistedHashes;
        public final Set<String> blacklistedHashes;
        public final boolean hasWhitelist;
        public final boolean hasBlacklist;
        
        public HashLists(Set<String> whitelistedHashes, Set<String> blacklistedHashes) {
            this.whitelistedHashes = whitelistedHashes != null ? whitelistedHashes : new HashSet<>();
            this.blacklistedHashes = blacklistedHashes != null ? blacklistedHashes : new HashSet<>();
            this.hasWhitelist = !this.whitelistedHashes.isEmpty();
            this.hasBlacklist = !this.blacklistedHashes.isEmpty();
        }
        
        /**
         * Validates a hash against the whitelist and blacklist
         * @param hash The hash to validate
         * @return ValidationResult indicating the result
         */
        public ValidationResult validateHash(String hash) {
            // Check blacklist first - if it's blacklisted, it's always invalid
            if (hasBlacklist && blacklistedHashes.contains(hash)) {
                return ValidationResult.BLACKLISTED;
            }
            
            // If there's a whitelist, the hash must be in it
            if (hasWhitelist) {
                if (whitelistedHashes.contains(hash)) {
                    return ValidationResult.WHITELISTED;
                } else {
                    return ValidationResult.NOT_WHITELISTED;
                }
            }
            
            // No whitelist and not blacklisted = allowed
            return ValidationResult.ALLOWED;
        }
    }
    
    public enum ValidationResult {
        WHITELISTED,    // Hash is in the whitelist
        BLACKLISTED,    // Hash is in the blacklist (forbidden)
        NOT_WHITELISTED, // Hash is not in the whitelist (forbidden when whitelist exists)
        ALLOWED         // Hash is allowed (no whitelist, not blacklisted)
    }
    
    /**
     * Fetches hash lists from a server URL
     * @param url The URL to fetch the markdown file from
     * @return HashLists object containing whitelist and blacklist
     */
    public static HashLists fetchHashLists(String url) {
        try {
            LOGGER.info("Fetching hash lists from: {}", url);
            
            String content = fetchUrlContent(url);
            if (content == null) {
                LOGGER.error("Failed to fetch content from URL: {}", url);
                return new HashLists(null, null);
            }
            
            return parseMarkdownHashLists(content);
            
        } catch (Exception e) {
            LOGGER.error("Error fetching hash lists from URL: {}", url, e);
            return new HashLists(null, null);
        }
    }
    
    /**
     * Fetches content from a URL
     */
    private static String fetchUrlContent(String urlString) throws IOException {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        try {
            // Set timeouts and request properties using configuration
            int timeoutMs = SecurityConfig.getConnectionTimeoutMs();
            connection.setConnectTimeout(timeoutMs);
            connection.setReadTimeout(timeoutMs);
            connection.setRequestMethod("GET");
            connection.setRequestProperty("User-Agent", "SarosSecurityCheckerMod/1.0");
            
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                LOGGER.error("HTTP request failed with response code: {}", responseCode);
                return null;
            }
            
            StringBuilder content = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
            }
            
            return content.toString();
            
        } finally {
            connection.disconnect();
        }
    }
    
    /**
     * Parses markdown content to extract whitelist and blacklist hashes
     * Expected format:
     * # Whitelist
     * - hash1
     * - hash2
     * 
     * # Blacklist
     * - hash3
     * - hash4
     */
    private static HashLists parseMarkdownHashLists(String content) {
        Set<String> whitelist = new HashSet<>();
        Set<String> blacklist = new HashSet<>();
        
        String[] lines = content.split("\n");
        String currentSection = null;
        
        // Regex pattern to match SHA-256 hashes (64 hex characters)
        Pattern hashPattern = Pattern.compile("([a-fA-F0-9]{64})");
        
        for (String line : lines) {
            line = line.trim();
            
            // Check for section headers
            if (line.toLowerCase().contains("whitelist")) {
                currentSection = "whitelist";
                LOGGER.debug("Found whitelist section");
                continue;
            } else if (line.toLowerCase().contains("blacklist")) {
                currentSection = "blacklist";
                LOGGER.debug("Found blacklist section");
                continue;
            }
            
            // Extract hashes from the current line
            if (currentSection != null && !line.isEmpty()) {
                Matcher matcher = hashPattern.matcher(line);
                while (matcher.find()) {
                    String hash = matcher.group(1).toLowerCase();
                    
                    if ("whitelist".equals(currentSection)) {
                        whitelist.add(hash);
                        LOGGER.debug("Added hash to whitelist: {}", hash);
                    } else if ("blacklist".equals(currentSection)) {
                        blacklist.add(hash);
                        LOGGER.debug("Added hash to blacklist: {}", hash);
                    }
                }
            }
        }
        
        LOGGER.info("Parsed hash lists - Whitelist: {} hashes, Blacklist: {} hashes", 
                   whitelist.size(), blacklist.size());
        
        return new HashLists(whitelist, blacklist);
    }
    
    /**
     * Validates multiple file hashes against the server hash lists
     */
    public static List<ValidationFailure> validateFiles(List<FileHashScanner.FileHashInfo> files, HashLists hashLists) {
        List<ValidationFailure> failures = new ArrayList<>();
        
        for (FileHashScanner.FileHashInfo file : files) {
            ValidationResult result = hashLists.validateHash(file.hash);
            
            if (result == ValidationResult.BLACKLISTED || result == ValidationResult.NOT_WHITELISTED) {
                failures.add(new ValidationFailure(file, result));
            }
        }
        
        return failures;
    }
    
    public static class ValidationFailure {
        public final FileHashScanner.FileHashInfo file;
        public final ValidationResult reason;
        
        public ValidationFailure(FileHashScanner.FileHashInfo file, ValidationResult reason) {
            this.file = file;
            this.reason = reason;
        }
        
        public String getErrorMessage() {
            switch (reason) {
                case BLACKLISTED:
                    return String.format("File '%s' is blacklisted by the server", file.fileName);
                case NOT_WHITELISTED:
                    return String.format("File '%s' is not in the server's whitelist", file.fileName);
                default:
                    return String.format("File '%s' failed validation", file.fileName);
            }
        }
    }
}
