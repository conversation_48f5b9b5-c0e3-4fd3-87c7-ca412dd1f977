# <PERSON>ro's Security Checker Mod

A client-side Minecraft Forge mod for version 1.20.1 that provides comprehensive file hash validation for mods and resource packs. This mod helps ensure server security by automatically scanning and validating local files against server-defined whitelists and blacklists.

## Features

- **Automatic File Hash Scanning**: Generates SHA-256 hashes for all files in `mods` and `resourcepacks` folders
- **Singleplayer Hash Display**: Shows file hashes in chat when joining singleplayer worlds
- **Multiplayer Security Validation**: Validates local files against server hash lists when connecting to multiplayer servers
- **Automatic Server Detection**: Automatically detects and downloads hash validation files from servers
- **Client-Side Disconnection**: Safely disconnects from servers when security violations are detected
- **Internationalization**: Supports English and German languages
- **Comprehensive Configuration**: Extensive configuration options for all features

## Installation

1. Download the latest release from the releases page
2. Place the `.jar` file in your Minecraft `mods` folder
3. Launch Minecraft with Forge 1.20.1

## Server Setup Guide

### For Server Administrators

To enable hash validation on your server, you need to create and host a `security-hashes.md` file that contains the allowed and prohibited file hashes.

#### Step 1: Create the Hash File

Create a file named `security-hashes.md` with the following format:

```markdown
# Whitelist
- 1a2b3c4d5e6f7890abcdef1234567890abcdef1234567890abcdef1234567890
- 2b3c4d5e6f7890abcdef1234567890abcdef1234567890abcdef1234567890ab
- 3c4d5e6f7890abcdef1234567890abcdef1234567890abcdef1234567890abcd

# Blacklist
- 9z8y7x6w5v4u3t2s1r0q9p8o7n6m5l4k3j2i1h0g9f8e7d6c5b4a3z2y1x0w9v
- 8y7x6w5v4u3t2s1r0q9p8o7n6m5l4k3j2i1h0g9f8e7d6c5b4a3z2y1x0w9v8u
```

**Important Notes:**
- Each hash must be exactly 64 characters (SHA-256)
- Hashes should be lowercase hexadecimal
- Use `# Whitelist` for allowed files
- Use `# Blacklist` for prohibited files
- You can have both sections, or just one
- Empty lines and comments are ignored

#### Step 2: Host the File

Place the `security-hashes.md` file on your web server at one of these locations:

**Primary locations (tried first):**
- `https://your-server.com/security-hashes.md`
- `https://your-server.com/security/security-hashes.md`
- `https://your-server.com/minecraft/security-hashes.md`
- `https://your-server.com/mods/security-hashes.md`

**Fallback locations (if HTTPS fails):**
- `http://your-server.com/security-hashes.md`
- `http://your-server.com/security/security-hashes.md`
- `http://your-server.com/minecraft/security-hashes.md`
- `http://your-server.com/mods/security-hashes.md`

Replace `your-server.com` with your actual server domain or IP address.

#### Step 3: Test the Setup

1. Place the hash file on your web server
2. Test access by visiting the URL in a browser
3. Join your server with the mod installed
4. Check the server logs and client logs for validation messages

### Hash File Generation

Use the included HTML Hash Generator Tool (see below) to easily generate hashes for your mod and resource pack files.

## Configuration

The mod creates a configuration file at `.minecraft/config/sarossecuritycheckermod-client.toml` with the following options:

### General Settings
- `enableSingleplayerScanning`: Enable hash scanning in singleplayer (default: true)
- `enableMultiplayerValidation`: Enable server validation in multiplayer (default: true)
- `showDetailedHashes`: Show full hashes vs shortened hashes (default: true)

### Server Settings
- `defaultHashListUrl`: Manual URL for hash lists (overrides auto-detection)
- `autoDetectServerHashFile`: Enable automatic server hash file detection (default: true)
- `serverHashFileName`: Filename to look for during auto-detection (default: "security-hashes.md")
- `tryHttpAndHttps`: Try both HTTP and HTTPS protocols (default: true)
- `autoDisconnectOnViolation`: Automatically disconnect on violations (default: true)
- `connectionTimeoutSeconds`: HTTP request timeout (default: 10)

### Display Settings
- `showChatNotifications`: Show messages in chat (default: true)
- `showSuccessMessages`: Show success messages (default: true)
- `maxFilesToDisplay`: Maximum files to show in chat (default: 50, 0 = unlimited)

## How It Works

### Singleplayer Mode
1. When joining a singleplayer world, the mod scans all files in `mods` and `resourcepacks` folders
2. Generates SHA-256 hashes for each file
3. Displays the hashes in chat with color-coded formatting

### Multiplayer Mode
1. When connecting to a multiplayer server, the mod first scans local files
2. Attempts to automatically detect and download the server's hash validation file
3. Compares local file hashes against the server's whitelist and blacklist
4. If violations are found:
   - Shows detailed error messages
   - Disconnects from the server (if auto-disconnect is enabled)
   - Provides specific information about which files are problematic

### Validation Logic
- **Whitelist**: If a whitelist exists, ALL local files must have hashes present in the whitelist
- **Blacklist**: If any local file hash matches the blacklist, validation fails
- **Combined**: Both whitelist and blacklist rules apply simultaneously

## Troubleshooting

### Common Issues

**"No server hash lists found - skipping validation"**
- The server doesn't have a hash validation file
- The hash file is not accessible at the expected URLs
- Check if the file exists and is publicly accessible

**"Auto-detection failed, trying configured URL"**
- Automatic detection couldn't find the hash file
- The mod will try the manually configured URL if set
- Consider setting a manual URL in the configuration

**"Security validation failed"**
- One or more local files are not allowed by the server
- Check the error message for specific file names and hashes
- Remove or replace the problematic files

**"Failed to validate files against server"**
- Network error or server timeout
- Check internet connection and server accessibility
- Increase the connection timeout in configuration

### Debug Steps

1. Check the Minecraft logs for detailed error messages
2. Verify the server's hash file is accessible in a web browser
3. Test with a minimal set of mods
4. Check the mod configuration file for correct settings
5. Try disabling auto-detection and setting a manual URL

### Getting Help

If you encounter issues:
1. Check the troubleshooting section above
2. Review the Minecraft logs for error details
3. Verify your server setup follows the guide exactly
4. Test with the HTML hash generator tool to ensure correct hash format

## Hash Generator Tool

The mod includes a standalone HTML-based hash generator tool for server administrators. See the separate `hash-generator.html` file for a user-friendly way to generate hash files for your server.

## Technical Details

- **Client-side only**: No server-side mod installation required
- **Hash Algorithm**: SHA-256
- **Supported Formats**: Markdown (.md) hash files
- **Network Protocol**: HTTP/HTTPS with configurable timeouts
- **File Types**: All files in mods and resourcepacks directories (recursive)
- **Languages**: English and German translations included

## License

All Rights Reserved

## Author

Saro

---

For more information, updates, and support, please visit the mod's official page.
